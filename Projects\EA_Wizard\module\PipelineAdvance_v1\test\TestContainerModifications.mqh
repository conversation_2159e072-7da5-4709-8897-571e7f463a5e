//+------------------------------------------------------------------+
//|                                        TestContainerModifications.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "../TradingPipelineContainer.mqh"
#include "../TradingPipelineContainerManager.mqh"

//+------------------------------------------------------------------+
//| 測試容器修改的簡單測試類                                         |
//+------------------------------------------------------------------+
class TestContainerModifications
{
public:
    // 測試 TradingPipelineContainer 移除 m_eventType
    static bool TestContainerWithoutEventType()
    {
        // 創建容器時不再需要 eventType 參數
        TradingPipelineContainer* container = new TradingPipelineContainer(
            "TestContainer",
            "測試容器",
            "TradingPipelineContainer",
            false,
            10
        );
        
        if(container == NULL)
        {
            Print("❌ 容器創建失敗");
            return false;
        }
        
        // 驗證容器基本功能
        string name = container.GetName();
        string type = container.GetType();
        bool executed = container.IsExecuted();
        
        if(name != "TestContainer")
        {
            Print("❌ 容器名稱不正確: ", name);
            delete container;
            return false;
        }
        
        if(type != "TradingPipelineContainer")
        {
            Print("❌ 容器類型不正確: ", type);
            delete container;
            return false;
        }
        
        if(executed)
        {
            Print("❌ 新創建的容器不應該已執行");
            delete container;
            return false;
        }
        
        Print("✅ TradingPipelineContainer 移除 m_eventType 測試通過");
        delete container;
        return true;
    }
    
    // 測試 TradingPipelineContainerManager 使用 HashMap
    static bool TestManagerWithHashMap()
    {
        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager(
            "TestManager",
            "ContainerManager",
            false,
            5
        );
        
        if(manager == NULL)
        {
            Print("❌ 管理器創建失敗");
            return false;
        }
        
        // 創建測試容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("Container1");
        TradingPipelineContainer* container2 = new TradingPipelineContainer("Container2");
        TradingPipelineContainer* container3 = new TradingPipelineContainer("Container3");
        
        // 測試添加容器
        if(!manager.AddContainer(container1))
        {
            Print("❌ 添加容器1失敗");
            delete manager;
            delete container1;
            delete container2;
            delete container3;
            return false;
        }
        
        if(!manager.AddContainer(container2))
        {
            Print("❌ 添加容器2失敗");
            delete manager;
            delete container1;
            delete container2;
            delete container3;
            return false;
        }
        
        if(!manager.AddContainer(container3))
        {
            Print("❌ 添加容器3失敗");
            delete manager;
            delete container1;
            delete container2;
            delete container3;
            return false;
        }
        
        // 測試容器數量
        if(manager.GetContainerCount() != 3)
        {
            Print("❌ 容器數量不正確: ", manager.GetContainerCount());
            delete manager;
            return false;
        }
        
        // 測試按名稱查找容器
        TradingPipelineContainer* found = manager.FindContainerByName("Container2");
        if(found == NULL)
        {
            Print("❌ 按名稱查找容器失敗");
            delete manager;
            return false;
        }
        
        if(found.GetName() != "Container2")
        {
            Print("❌ 找到的容器名稱不正確: ", found.GetName());
            delete manager;
            return false;
        }
        
        // 測試 GetContainer 方法
        TradingPipelineContainer* getContainer = manager.GetContainer("Container1");
        if(getContainer == NULL)
        {
            Print("❌ GetContainer 方法失敗");
            delete manager;
            return false;
        }
        
        if(getContainer.GetName() != "Container1")
        {
            Print("❌ GetContainer 返回的容器名稱不正確: ", getContainer.GetName());
            delete manager;
            return false;
        }
        
        // 測試移除容器
        if(!manager.RemoveContainerByName("Container2"))
        {
            Print("❌ 按名稱移除容器失敗");
            delete manager;
            return false;
        }
        
        if(manager.GetContainerCount() != 2)
        {
            Print("❌ 移除後容器數量不正確: ", manager.GetContainerCount());
            delete manager;
            return false;
        }
        
        // 測試 ExecuteAll
        manager.ExecuteAll();
        if(!manager.IsExecuted())
        {
            Print("❌ ExecuteAll 後管理器應該已執行");
            delete manager;
            return false;
        }
        
        // 測試 RestoreAll
        manager.RestoreAll();
        if(manager.IsExecuted())
        {
            Print("❌ RestoreAll 後管理器不應該已執行");
            delete manager;
            return false;
        }
        
        Print("✅ TradingPipelineContainerManager 使用 HashMap 測試通過");
        delete manager;
        return true;
    }
    
    // 運行所有測試
    static bool RunAllTests()
    {
        Print("=== 開始測試容器修改 ===");
        
        bool test1 = TestContainerWithoutEventType();
        bool test2 = TestManagerWithHashMap();
        
        bool allPassed = test1 && test2;
        
        if(allPassed)
        {
            Print("✅ 所有測試通過！");
        }
        else
        {
            Print("❌ 部分測試失敗");
        }
        
        Print("=== 測試完成 ===");
        return allPassed;
    }
};
