//+------------------------------------------------------------------+
//|                                              TradingPipeline.mqh |
//|                                            EAPipelineAdvance     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "TradingEvent.mqh"

// 前向聲明
class TradingPipelineContainerManager;

//+------------------------------------------------------------------+
//| 交易流水線接口                                                   |
//+------------------------------------------------------------------+
interface ITradingPipeline
{
    // 執行流水線
    void Execute();

    // 獲取流水線名稱
    string GetName();

    // 獲取流水線類型
    string GetType();

    // 檢查是否已執行
    bool IsExecuted();

    // 重置流水線狀態
    void Restore();
};

//+------------------------------------------------------------------+
//| 流水線執行結果                                                   |
//+------------------------------------------------------------------+
class PipelineResult
{
private:
    bool m_success;                 // 執行是否成功
    string m_message;               // 結果消息
    string m_source;                // 來源流水線
    datetime m_timestamp;           // 執行時間戳
    ENUM_ERROR_LEVEL m_errorLevel;  // 錯誤級別

public:
    // 構造函數
    PipelineResult(bool success, string message, string source, ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO)
        : m_success(success),
          m_message(message),
          m_source(source),
          m_timestamp(TimeCurrent()),
          m_errorLevel(errorLevel)
    {
    }

    // 析構函數
    ~PipelineResult() {}

    // 獲取執行結果
    bool IsSuccess() const { return m_success; }

    // 獲取消息
    string GetMessage() const { return m_message; }

    // 獲取來源
    string GetSource() const { return m_source; }

    // 獲取時間戳
    datetime GetTimestamp() const { return m_timestamp; }

    // 獲取錯誤級別
    ENUM_ERROR_LEVEL GetErrorLevel() const { return m_errorLevel; }

    // 轉換為字符串
    string ToString() const
    {
        return StringFormat("[%s] %s: %s (Level: %d)",
                          TimeToString(m_timestamp),
                          m_source,
                          m_message,
                          m_errorLevel);
    }
};

//+------------------------------------------------------------------+
//| 交易流水線基類                                                   |
//+------------------------------------------------------------------+
class TradingPipeline : public ITradingPipeline
{
protected:
    string m_name;                  // 流水線名稱
    string m_type;                  // 流水線類型
    bool m_executed;                // 執行狀態
    ENUM_TRADING_STAGE m_stage;     // 交易階段
    TradingPipelineContainerManager* m_manager; // 管理器引用

public:
    // 建構函數
    TradingPipeline(string name = "",
                   string type = "TradingPipeline",
                   ENUM_TRADING_STAGE stage = INIT_START,
                   TradingPipelineContainerManager* manager = NULL)
        : m_name(name),
          m_type(type),
          m_executed(false),
          m_stage(stage),
          m_manager(manager)
          {}

    // 解構函數
    virtual ~TradingPipeline() {}

    // 執行流水線
    virtual void Execute() {
        if(m_executed) return;

        Main();
        m_executed = true;
    }

    // 獲取流水線名稱
    string GetName() { return m_name; }

    // 獲取流水線類型
    string GetType() { return m_type; }

    // 檢查是否已執行
    bool IsExecuted() { return m_executed; }

    // 重置流水線狀態
    virtual void Restore() { m_executed = false; }

    // 獲取交易階段
    ENUM_TRADING_STAGE GetStage() const { return m_stage; }

    // 獲取管理器
    TradingPipelineContainerManager* GetManager() const { return m_manager; }

protected:
    // 主程序 - 子類必須實現
    virtual void Main() = 0;
};

//+------------------------------------------------------------------+
//| 簡單交易流水線實現示例                                           |
//+------------------------------------------------------------------+
class SimpleTradingPipeline : public TradingPipeline
{
};