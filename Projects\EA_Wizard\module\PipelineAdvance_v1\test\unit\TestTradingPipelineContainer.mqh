//+------------------------------------------------------------------+
//|                                    TestTradingPipelineContainer.mqh |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 測試用的簡單交易流水線                                           |
//+------------------------------------------------------------------+
class TestContainerTradingPipeline : public TradingPipeline
{
private:
    string m_testMessage;
    bool m_shouldFail;

public:
    TestContainerTradingPipeline(string name, string testMessage = "測試執行", bool shouldFail = false)
        : TradingPipeline(name, "TestPipeline"),
          m_testMessage(testMessage),
          m_shouldFail(shouldFail)
    {
    }

protected:
    virtual void Main() override
    {
        if(m_shouldFail)
        {
            Print(StringFormat("[%s] 模擬執行失敗: %s", GetName(), m_testMessage));
        }
        else
        {
            Print(StringFormat("[%s] 執行成功: %s", GetName(), m_testMessage));
        }
    }
};

//+------------------------------------------------------------------+
//| TradingPipelineContainer 單元測試類                              |
//+------------------------------------------------------------------+
class TestTradingPipelineContainerCase : public TestCase
{
private:
    TestRunner* m_runner;
    bool m_ownsRunner;

public:
    // 構造函數 - 可以接受外部 TestRunner 或創建內部 TestRunner
    TestTradingPipelineContainerCase(TestRunner* externalRunner = NULL) : TestCase("TestTradingPipelineContainer")
    {
        if(externalRunner != NULL)
        {
            m_runner = externalRunner;
            m_ownsRunner = false;
        }
        else
        {
            m_runner = new TestRunner();
            m_ownsRunner = true;
        }
    }

    ~TestTradingPipelineContainerCase()
    {
        if(m_ownsRunner && m_runner != NULL)
        {
            delete m_runner;
            m_runner = NULL;
        }
    }

    virtual void RunTests() override
    {
        TestConstructor();
        TestBasicProperties();
        TestAddPipeline();
        TestRemovePipeline();
        TestFindPipeline();
        TestExecute();
        TestEventType();
        TestMaxPipelinesLimit();
        TestClear();
        TestEdgeCases();

        // 只有當擁有內部 TestRunner 時才顯示摘要
        if(m_ownsRunner)
        {
            m_runner.ShowSummary();
        }
    }

protected:
    // 記錄測試結果的輔助方法
    void RecordResult(TestResult* result)
    {
        m_runner.RecordResult(result);
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試構造函數 ---");

        // 測試默認構造函數
        TradingPipelineContainer* container1 = new TradingPipelineContainer("測試容器1");
        RecordResult(Assert::AssertNotNull("構造函數_默認參數", container1));
        RecordResult(Assert::AssertEquals("構造函數_名稱", "測試容器1", container1.GetName()));
        RecordResult(Assert::AssertEquals("構造函數_類型", "TradingPipelineContainer", container1.GetType()));
        RecordResult(Assert::AssertEquals("構造函數_事件類型", (int)TRADING_TICK, (int)container1.GetEventType()));
        RecordResult(Assert::AssertTrue("構造函數_初始啟用", container1.IsEnabled()));
        RecordResult(Assert::AssertTrue("構造函數_初始為空", container1.IsEmpty()));
        delete container1;

        // 測試完整參數構造函數
        TradingPipelineContainer* container2 = new TradingPipelineContainer(
            "測試容器2", "測試描述", "CustomType", TRADING_INIT, true, 20);
        RecordResult(Assert::AssertNotNull("構造函數_完整參數", container2));
        RecordResult(Assert::AssertEquals("構造函數_描述", "測試描述", container2.GetDescription()));
        RecordResult(Assert::AssertEquals("構造函數_自定義類型", "CustomType", container2.GetType()));
        RecordResult(Assert::AssertEquals("構造函數_自定義事件", (int)TRADING_INIT, (int)container2.GetEventType()));
        RecordResult(Assert::AssertEquals("構造函數_最大容量", 20, container2.GetMaxPipelines()));
        delete container2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試基本屬性 ---");

        TradingPipelineContainer* container = new TradingPipelineContainer(
            "屬性測試容器", "用於測試屬性", "PropertyTest", TRADING_TICK, false, 15);

        RecordResult(Assert::AssertEquals("屬性_名稱", "屬性測試容器", container.GetName()));
        RecordResult(Assert::AssertEquals("屬性_描述", "用於測試屬性", container.GetDescription()));
        RecordResult(Assert::AssertEquals("屬性_類型", "PropertyTest", container.GetType()));
        RecordResult(Assert::AssertEquals("屬性_事件類型", (int)TRADING_TICK, (int)container.GetEventType()));
        RecordResult(Assert::AssertEquals("屬性_最大容量", 15, container.GetMaxPipelines()));
        RecordResult(Assert::AssertEquals("屬性_初始數量", 0, container.GetPipelineCount()));
        RecordResult(Assert::AssertTrue("屬性_初始為空", container.IsEmpty()));
        RecordResult(Assert::AssertFalse("屬性_初始未滿", container.IsFull()));
        RecordResult(Assert::AssertFalse("屬性_初始未執行", container.IsExecuted()));

        delete container;
    }

    // 測試添加流水線
    void TestAddPipeline()
    {
        Print("--- 測試添加流水線 ---");

        TradingPipelineContainer* container = new TradingPipelineContainer("添加測試容器", "", "AddTest", TRADING_TICK, false, 3);

        // 測試添加有效流水線
        TestContainerTradingPipeline* pipeline1 = new TestContainerTradingPipeline("流水線1", "測試消息1");
        TestContainerTradingPipeline* pipeline2 = new TestContainerTradingPipeline("流水線2", "測試消息2");
        TestContainerTradingPipeline* pipeline3 = new TestContainerTradingPipeline("流水線3", "測試消息3");

        RecordResult(Assert::AssertTrue("添加_第一個流水線", container.AddPipeline(pipeline1)));
        RecordResult(Assert::AssertEquals("添加_數量檢查1", 1, container.GetPipelineCount()));
        RecordResult(Assert::AssertFalse("添加_不再為空", container.IsEmpty()));

        RecordResult(Assert::AssertTrue("添加_第二個流水線", container.AddPipeline(pipeline2)));
        RecordResult(Assert::AssertEquals("添加_數量檢查2", 2, container.GetPipelineCount()));

        RecordResult(Assert::AssertTrue("添加_第三個流水線", container.AddPipeline(pipeline3)));
        RecordResult(Assert::AssertEquals("添加_數量檢查3", 3, container.GetPipelineCount()));
        RecordResult(Assert::AssertTrue("添加_已滿", container.IsFull()));

        // 測試添加超過容量
        TestContainerTradingPipeline* pipeline4 = new TestContainerTradingPipeline("流水線4", "測試消息4");
        RecordResult(Assert::AssertFalse("添加_超過容量", container.AddPipeline(pipeline4)));
        RecordResult(Assert::AssertEquals("添加_數量不變", 3, container.GetPipelineCount()));
        delete pipeline4;

        // 測試添加 NULL
        RecordResult(Assert::AssertFalse("添加_NULL流水線", container.AddPipeline(NULL)));

        delete container;
    }

    // 測試移除流水線
    void TestRemovePipeline()
    {
        Print("--- 測試移除流水線 ---");

        TradingPipelineContainer* container = new TradingPipelineContainer("移除測試容器", "", "RemoveTest", TRADING_TICK, false, 5);

        // 添加測試流水線
        TestContainerTradingPipeline* pipeline1 = new TestContainerTradingPipeline("移除測試1", "測試消息1");
        TestContainerTradingPipeline* pipeline2 = new TestContainerTradingPipeline("移除測試2", "測試消息2");
        container.AddPipeline(pipeline1);
        container.AddPipeline(pipeline2);

        RecordResult(Assert::AssertEquals("移除_初始數量", 2, container.GetPipelineCount()));

        // 測試按名稱移除
        RecordResult(Assert::AssertTrue("移除_按名稱成功", container.RemovePipelineByName("移除測試1")));
        RecordResult(Assert::AssertEquals("移除_數量減少", 1, container.GetPipelineCount()));

        // 測試移除不存在的流水線
        RecordResult(Assert::AssertFalse("移除_不存在", container.RemovePipelineByName("不存在的流水線")));
        RecordResult(Assert::AssertEquals("移除_數量不變", 1, container.GetPipelineCount()));

        // 測試移除剩餘流水線
        RecordResult(Assert::AssertTrue("移除_最後一個", container.RemovePipelineByName("移除測試2")));
        RecordResult(Assert::AssertEquals("移除_變為空", 0, container.GetPipelineCount()));
        RecordResult(Assert::AssertTrue("移除_確認為空", container.IsEmpty()));

        delete container;
    }

    // 測試查找流水線
    void TestFindPipeline()
    {
        Print("--- 測試查找流水線 ---");

        TradingPipelineContainer* container = new TradingPipelineContainer("查找測試容器");

        // 添加測試流水線
        TestContainerTradingPipeline* pipeline1 = new TestContainerTradingPipeline("查找測試1", "測試消息1");
        TestContainerTradingPipeline* pipeline2 = new TestContainerTradingPipeline("查找測試2", "測試消息2");
        container.AddPipeline(pipeline1);
        container.AddPipeline(pipeline2);

        // 測試查找存在的流水線
        ITradingPipeline* found1 = container.FindByName("查找測試1");
        RecordResult(Assert::AssertNotNull("查找_存在流水線1", found1));
        if(found1 != NULL)
        {
            RecordResult(Assert::AssertEquals("查找_名稱匹配1", "查找測試1", found1.GetName()));
        }

        ITradingPipeline* found2 = container.FindByName("查找測試2");
        RecordResult(Assert::AssertNotNull("查找_存在流水線2", found2));
        if(found2 != NULL)
        {
            RecordResult(Assert::AssertEquals("查找_名稱匹配2", "查找測試2", found2.GetName()));
        }

        // 測試查找不存在的流水線
        ITradingPipeline* notFound = container.FindByName("不存在的流水線");
        RecordResult(Assert::AssertNull("查找_不存在流水線", notFound));

        // 測試 HasPipelineByName
        RecordResult(Assert::AssertTrue("查找_包含檢查1", container.HasPipelineByName("查找測試1")));
        RecordResult(Assert::AssertTrue("查找_包含檢查2", container.HasPipelineByName("查找測試2")));
        RecordResult(Assert::AssertFalse("查找_不包含檢查", container.HasPipelineByName("不存在的流水線")));

        delete container;
    }

    // 測試執行功能
    void TestExecute()
    {
        Print("--- 測試執行功能 ---");

        TradingPipelineContainer* container = new TradingPipelineContainer("執行測試容器");

        // 添加測試流水線
        TestContainerTradingPipeline* pipeline1 = new TestContainerTradingPipeline("執行測試1", "執行消息1", false);
        TestContainerTradingPipeline* pipeline2 = new TestContainerTradingPipeline("執行測試2", "執行消息2", false);
        container.AddPipeline(pipeline1);
        container.AddPipeline(pipeline2);

        RecordResult(Assert::AssertFalse("執行_初始未執行", container.IsExecuted()));

        // 執行容器
        container.Execute();

        RecordResult(Assert::AssertTrue("執行_容器已執行", container.IsExecuted()));
        RecordResult(Assert::AssertTrue("執行_流水線1已執行", pipeline1.IsExecuted()));
        RecordResult(Assert::AssertTrue("執行_流水線2已執行", pipeline2.IsExecuted()));

        // 測試重複執行防護
        container.Execute();
        RecordResult(Assert::AssertTrue("執行_重複執行防護", container.IsExecuted()));

        delete container;
    }

    // 測試事件類型
    void TestEventType()
    {
        Print("--- 測試事件類型 ---");

        TradingPipelineContainer* initContainer = new TradingPipelineContainer("初始化容器", "", "InitTest", TRADING_INIT);
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer("Tick容器", "", "TickTest", TRADING_TICK);
        TradingPipelineContainer* deinitContainer = new TradingPipelineContainer("清理容器", "", "DeinitTest", TRADING_DEINIT);

        RecordResult(Assert::AssertEquals("事件_初始化類型", (int)TRADING_INIT, (int)initContainer.GetEventType()));
        RecordResult(Assert::AssertEquals("事件_Tick類型", (int)TRADING_TICK, (int)tickContainer.GetEventType()));
        RecordResult(Assert::AssertEquals("事件_清理類型", (int)TRADING_DEINIT, (int)deinitContainer.GetEventType()));

        delete initContainer;
        delete tickContainer;
        delete deinitContainer;
    }

    // 測試最大流水線限制
    void TestMaxPipelinesLimit()
    {
        Print("--- 測試最大流水線限制 ---");

        TradingPipelineContainer* container = new TradingPipelineContainer("限制測試容器", "", "LimitTest", TRADING_TICK, false, 2);

        RecordResult(Assert::AssertEquals("限制_最大容量", 2, container.GetMaxPipelines()));

        // 添加到最大容量
        TestContainerTradingPipeline* pipeline1 = new TestContainerTradingPipeline("限制測試1");
        TestContainerTradingPipeline* pipeline2 = new TestContainerTradingPipeline("限制測試2");
        TestContainerTradingPipeline* pipeline3 = new TestContainerTradingPipeline("限制測試3");

        RecordResult(Assert::AssertTrue("限制_添加第1個", container.AddPipeline(pipeline1)));
        RecordResult(Assert::AssertTrue("限制_添加第2個", container.AddPipeline(pipeline2)));
        RecordResult(Assert::AssertTrue("限制_已滿", container.IsFull()));

        // 嘗試添加超過限制
        RecordResult(Assert::AssertFalse("限制_超過限制", container.AddPipeline(pipeline3)));
        RecordResult(Assert::AssertEquals("限制_數量不變", 2, container.GetPipelineCount()));

        delete pipeline3;
        delete container;
    }

    // 測試清理功能
    void TestClear()
    {
        Print("--- 測試清理功能 ---");

        TradingPipelineContainer* container = new TradingPipelineContainer("清理測試容器");

        // 添加測試流水線
        TestContainerTradingPipeline* pipeline1 = new TestContainerTradingPipeline("清理測試1");
        TestContainerTradingPipeline* pipeline2 = new TestContainerTradingPipeline("清理測試2");
        container.AddPipeline(pipeline1);
        container.AddPipeline(pipeline2);

        RecordResult(Assert::AssertEquals("清理_添加後數量", 2, container.GetPipelineCount()));
        RecordResult(Assert::AssertFalse("清理_添加後非空", container.IsEmpty()));

        // 執行清理
        container.Clear();

        RecordResult(Assert::AssertEquals("清理_清理後數量", 0, container.GetPipelineCount()));
        RecordResult(Assert::AssertTrue("清理_清理後為空", container.IsEmpty()));

        delete container;
    }

    // 測試邊界情況
    void TestEdgeCases()
    {
        Print("--- 測試邊界情況 ---");

        TradingPipelineContainer* container = new TradingPipelineContainer("邊界測試容器");

        // 測試空容器執行
        container.Execute();
        RecordResult(Assert::AssertTrue("邊界_空容器執行", container.IsExecuted()));

        // 測試禁用容器
        container.SetEnabled(false);
        container.Restore();
        container.Execute();
        RecordResult(Assert::AssertFalse("邊界_禁用容器不執行", container.IsExecuted()));

        // 重新啟用
        container.SetEnabled(true);
        container.Execute();
        RecordResult(Assert::AssertTrue("邊界_重新啟用執行", container.IsExecuted()));

        delete container;
    }
};

//+------------------------------------------------------------------+
//| 運行 TradingPipelineContainer 測試                               |
//+------------------------------------------------------------------+
void RunTestTradingPipelineContainer()
{
    Print("\n" + StringRepeat("=", 60));
    Print("  TradingPipelineContainer 單元測試");
    Print(StringRepeat("=", 60));

    TestRunner* runner = new TestRunner();
    TestTradingPipelineContainerCase* testCase = new TestTradingPipelineContainerCase(runner);

    runner.RunTestCase(testCase);
    runner.ShowSummary();

    delete testCase;
    delete runner;

    Print(StringRepeat("=", 60));
    Print("  TradingPipelineContainer 單元測試完成");
    Print(StringRepeat("=", 60));
}
