//+------------------------------------------------------------------+
//|                        TestTradingPipelineContainerManager_Updated.mqh |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineContainerManager.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 簡單測試流水線類 - 用於測試                                       |
//+------------------------------------------------------------------+
class TestTradingPipeline : public TradingPipeline
{
private:
    bool m_shouldSucceed;
    string m_customMessage;

public:
    TestTradingPipeline(string name,
                       bool shouldSucceed = true,
                       string customMessage = "",
                       string type = "TestTradingPipeline")
        : TradingPipeline(name, type),
          m_shouldSucceed(shouldSucceed),
          m_customMessage(customMessage)
    {
        if(m_customMessage == "")
        {
            m_customMessage = shouldSucceed ? "測試執行成功" : "測試執行失敗";
        }
    }

protected:
    virtual void Main() override
    {
        if(m_shouldSucceed)
        {
            Print("TestTradingPipeline: 成功執行 - " + GetName() + " (" + m_customMessage + ")");
        }
        else
        {
            Print("TestTradingPipeline: 執行失敗 - " + GetName() + " (" + m_customMessage + ")");
        }
    }
};

//+------------------------------------------------------------------+
//| TradingPipelineContainerManager測試類別                         |
//+------------------------------------------------------------------+
class TestTradingPipelineContainerManager_Updated : public TestCase
{
private:
    TestRunner* m_runner;
    bool m_ownsRunner;

public:
    // 構造函數 - 可以接受外部 TestRunner 或創建內部 TestRunner
    TestTradingPipelineContainerManager_Updated(TestRunner* externalRunner = NULL) : TestCase("TestTradingPipelineContainerManager_Updated")
    {
        if(externalRunner != NULL)
        {
            m_runner = externalRunner;
            m_ownsRunner = false;
        }
        else
        {
            m_runner = new TestRunner();
            m_ownsRunner = true;
        }
    }

    // 析構函數
    ~TestTradingPipelineContainerManager_Updated()
    {
        if(m_ownsRunner && m_runner != NULL)
        {
            delete m_runner;
            m_runner = NULL;
        }
    }

    // 運行所有測試
    void RunTests() override
    {
        TestConstructor();
        TestBasicProperties();
        TestAddContainer();
        TestRemoveContainer();
        TestMaxContainersLimit();
        TestExecuteByEventType();
        TestRestoreByEventType();
        TestFindContainerByName();
        TestGetAllContainers();
        TestClear();
        TestEdgeCases();

        // 只有當擁有內部 TestRunner 時才顯示摘要
        if(m_ownsRunner)
        {
            m_runner.ShowSummary();
        }
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("=== 測試構造函數 ===");

        // 測試默認構造函數
        TradingPipelineContainerManager* manager1 = new TradingPipelineContainerManager();
        m_runner.RecordResult(Assert::AssertEquals("默認構造_名稱", "TradingPipelineContainerManager", manager1.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("默認構造_類型", "ContainerManager", manager1.GetType()));
        m_runner.RecordResult(Assert::AssertFalse("默認構造_未執行", manager1.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("默認構造_已啟用", manager1.IsEnabled()));
        delete manager1;

        // 測試自定義構造函數
        TradingPipelineContainerManager* manager2 = new TradingPipelineContainerManager("測試管理器", "自定義類型", true, 5);
        m_runner.RecordResult(Assert::AssertEquals("自定義構造_名稱", "測試管理器", manager2.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("自定義構造_類型", "自定義類型", manager2.GetType()));
        delete manager2;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("=== 測試基本屬性 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("屬性測試");

        // 測試基本屬性
        m_runner.RecordResult(Assert::AssertEquals("最大容器數量", 10, manager.GetMaxContainers()));
        m_runner.RecordResult(Assert::AssertEquals("初始容器數量", 0, manager.GetContainerCount()));
        m_runner.RecordResult(Assert::AssertTrue("初始有空位置", manager.HasEmptySlot()));

        // 測試啟用/禁用
        manager.SetEnabled(false);
        m_runner.RecordResult(Assert::AssertFalse("禁用後狀態", manager.IsEnabled()));
        manager.SetEnabled(true);
        m_runner.RecordResult(Assert::AssertTrue("重新啟用後狀態", manager.IsEnabled()));

        delete manager;
    }

    // 測試添加容器
    void TestAddContainer()
    {
        Print("=== 測試添加容器 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("添加測試");

        // 創建測試容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("容器1", "第一個容器", "Container", TRADING_INIT);
        TradingPipelineContainer* container2 = new TradingPipelineContainer("容器2", "第二個容器", "Container", TRADING_TICK);

        // 測試添加容器
        m_runner.RecordResult(Assert::AssertTrue("添加容器1", manager.AddContainer(container1)));
        m_runner.RecordResult(Assert::AssertEquals("添加後容器數量1", 1, manager.GetContainerCount()));

        m_runner.RecordResult(Assert::AssertTrue("添加容器2", manager.AddContainer(container2)));
        m_runner.RecordResult(Assert::AssertEquals("添加後容器數量2", 2, manager.GetContainerCount()));

        // 測試添加NULL容器
        m_runner.RecordResult(Assert::AssertFalse("添加NULL容器失敗", manager.AddContainer(NULL)));

        // 測試添加重複名稱的容器
        TradingPipelineContainer* duplicateContainer = new TradingPipelineContainer("容器1", "重複名稱容器", "Container", TRADING_TICK);
        m_runner.RecordResult(Assert::AssertFalse("添加重複名稱容器失敗", manager.AddContainer(duplicateContainer)));

        delete duplicateContainer;
        delete manager;
    }

    // 測試移除容器
    void TestRemoveContainer()
    {
        Print("=== 測試移除容器 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("移除測試");

        // 創建並添加測試容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("移除容器1", "第一個容器", "Container", TRADING_INIT);
        TradingPipelineContainer* container2 = new TradingPipelineContainer("移除容器2", "第二個容器", "Container", TRADING_TICK);

        manager.AddContainer(container1);
        manager.AddContainer(container2);

        // 測試移除容器
        m_runner.RecordResult(Assert::AssertTrue("移除容器1", manager.RemoveContainer(container1)));
        m_runner.RecordResult(Assert::AssertEquals("移除後容器數量", 1, manager.GetContainerCount()));

        m_runner.RecordResult(Assert::AssertTrue("移除容器2", manager.RemoveContainer(container2)));
        m_runner.RecordResult(Assert::AssertEquals("全部移除後容器數量", 0, manager.GetContainerCount()));
        m_runner.RecordResult(Assert::AssertTrue("移除後有空位置", manager.HasEmptySlot()));

        // 測試移除不存在的容器
        TradingPipelineContainer* nonExistentContainer = new TradingPipelineContainer("不存在容器", "不存在的容器", "Container", TRADING_TICK);
        m_runner.RecordResult(Assert::AssertFalse("移除不存在容器失敗", manager.RemoveContainer(nonExistentContainer)));

        // 測試移除NULL容器
        m_runner.RecordResult(Assert::AssertFalse("移除NULL容器失敗", manager.RemoveContainer(NULL)));

        delete nonExistentContainer;
        delete container1;
        delete container2;
        delete manager;
    }

    // 測試最大容器數限制
    void TestMaxContainersLimit()
    {
        Print("=== 測試最大容器數限制 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("限制測試", "ContainerManager", false, 3);

        // 添加到最大數量
        for(int i = 1; i <= 3; i++)
        {
            TradingPipelineContainer* container = new TradingPipelineContainer("限制容器" + IntegerToString(i), "測試容器", "Container", TRADING_TICK);
            bool added = manager.AddContainer(container);
            m_runner.RecordResult(Assert::AssertTrue("添加容器" + IntegerToString(i), added));

            if(!added) delete container;
        }

        m_runner.RecordResult(Assert::AssertEquals("達到最大容器數", 3, manager.GetContainerCount()));
        m_runner.RecordResult(Assert::AssertFalse("達到最大後無空位置", manager.HasEmptySlot()));

        // 嘗試添加超過限制的容器
        TradingPipelineContainer* extraContainer = new TradingPipelineContainer("額外容器", "超過限制的容器", "Container", TRADING_TICK);
        m_runner.RecordResult(Assert::AssertFalse("超過限制添加失敗", manager.AddContainer(extraContainer)));

        delete extraContainer;
        delete manager;
    }

    // 測試按事件類型執行
    void TestExecuteByEventType()
    {
        Print("=== 測試按事件類型執行 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("執行測試");

        // 創建不同事件類型的容器
        TradingPipelineContainer* initContainer = new TradingPipelineContainer("初始化容器", "INIT事件容器", "Container", TRADING_INIT);
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer("Tick容器", "TICK事件容器", "Container", TRADING_TICK);

        // 為每個容器添加流水線
        TestTradingPipeline* initPipeline = new TestTradingPipeline("初始化流水線");
        TestTradingPipeline* tickPipeline = new TestTradingPipeline("Tick流水線");

        initContainer.AddPipeline(initPipeline);
        tickContainer.AddPipeline(tickPipeline);

        manager.AddContainer(initContainer);
        manager.AddContainer(tickContainer);

        // 測試執行INIT事件
        manager.Execute(TRADING_INIT);
        m_runner.RecordResult(Assert::AssertTrue("INIT執行後管理器已執行", manager.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("INIT容器已執行", initContainer.IsExecuted()));
        m_runner.RecordResult(Assert::AssertFalse("TICK容器未執行", tickContainer.IsExecuted()));

        delete manager;
    }

    // 測試按事件類型重置
    void TestRestoreByEventType()
    {
        Print("=== 測試按事件類型重置 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("重置測試");

        // 創建測試容器和流水線
        TradingPipelineContainer* container1 = new TradingPipelineContainer("重置容器1", "TICK事件容器", "Container", TRADING_TICK);
        TradingPipelineContainer* container2 = new TradingPipelineContainer("重置容器2", "INIT事件容器", "Container", TRADING_INIT);

        TestTradingPipeline* pipeline1 = new TestTradingPipeline("重置流水線1");
        TestTradingPipeline* pipeline2 = new TestTradingPipeline("重置流水線2");

        container1.AddPipeline(pipeline1);
        container2.AddPipeline(pipeline2);

        manager.AddContainer(container1);
        manager.AddContainer(container2);

        // 執行兩種事件類型
        manager.Execute(TRADING_TICK);
        manager.RestoreAll();
        manager.Execute(TRADING_INIT);

        // 測試按事件類型重置
        manager.Restore(TRADING_TICK);
        m_runner.RecordResult(Assert::AssertFalse("TICK容器已重置", container1.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("INIT容器仍執行", container2.IsExecuted()));

        manager.Restore(TRADING_INIT);
        m_runner.RecordResult(Assert::AssertFalse("INIT容器已重置", container2.IsExecuted()));

        delete manager;
    }

    // 測試按名稱查找容器
    void TestFindContainerByName()
    {
        Print("=== 測試按名稱查找容器 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("查找測試");

        // 創建測試容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("查找容器1", "第一個容器", "Container", TRADING_TICK);
        TradingPipelineContainer* container2 = new TradingPipelineContainer("查找容器2", "第二個容器", "Container", TRADING_INIT);

        manager.AddContainer(container1);
        manager.AddContainer(container2);

        // 測試查找存在的容器
        TradingPipelineContainer* found1 = manager.FindContainerByName("查找容器1");
        m_runner.RecordResult(Assert::AssertNotNull("找到容器1", found1));
        if(found1 != NULL)
        {
            m_runner.RecordResult(Assert::AssertEquals("容器1名稱正確", "查找容器1", found1.GetName()));
        }

        TradingPipelineContainer* found2 = manager.FindContainerByName("查找容器2");
        m_runner.RecordResult(Assert::AssertNotNull("找到容器2", found2));

        // 測試查找不存在的容器
        TradingPipelineContainer* notFound = manager.FindContainerByName("不存在的容器");
        m_runner.RecordResult(Assert::AssertNull("未找到不存在容器", notFound));

        delete manager;
    }

    // 測試獲取所有容器
    void TestGetAllContainers()
    {
        Print("=== 測試獲取所有容器 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("獲取所有容器測試");

        // 測試空管理器
        TradingPipelineContainer* emptyContainers[];
        int emptyCount = manager.GetAllContainers(emptyContainers);
        m_runner.RecordResult(Assert::AssertEquals("空管理器容器數量", 0, emptyCount));

        // 添加容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("全部容器1", "第一個容器", "Container", TRADING_TICK);
        TradingPipelineContainer* container2 = new TradingPipelineContainer("全部容器2", "第二個容器", "Container", TRADING_INIT);

        manager.AddContainer(container1);
        manager.AddContainer(container2);

        // 測試獲取所有容器
        TradingPipelineContainer* allContainers[];
        int count = manager.GetAllContainers(allContainers);
        m_runner.RecordResult(Assert::AssertEquals("獲取容器數量", 2, count));

        delete manager;
    }

    // 測試清理功能
    void TestClear()
    {
        Print("=== 測試清理功能 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("清理測試", "清理類型", true);

        // 添加容器
        TradingPipelineContainer* container1 = new TradingPipelineContainer("清理容器1", "第一個容器", "Container", TRADING_TICK);
        TradingPipelineContainer* container2 = new TradingPipelineContainer("清理容器2", "第二個容器", "Container", TRADING_INIT);

        manager.AddContainer(container1);
        manager.AddContainer(container2);

        m_runner.RecordResult(Assert::AssertEquals("清理前容器數量", 2, manager.GetContainerCount()));

        // 測試清理
        manager.Clear();
        m_runner.RecordResult(Assert::AssertEquals("清理後容器數量", 0, manager.GetContainerCount()));
        m_runner.RecordResult(Assert::AssertTrue("清理後有空位置", manager.HasEmptySlot()));
        m_runner.RecordResult(Assert::AssertFalse("清理後未執行", manager.IsExecuted()));

        delete manager;
    }

    // 測試邊界情況
    void TestEdgeCases()
    {
        Print("=== 測試邊界情況 ===");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("邊界測試");

        // 測試禁用狀態下的執行
        manager.SetEnabled(false);
        TradingPipelineContainer* container = new TradingPipelineContainer("邊界容器", "測試容器", "Container", TRADING_TICK);
        manager.AddContainer(container);

        manager.Execute(TRADING_TICK);
        m_runner.RecordResult(Assert::AssertFalse("禁用時不執行", manager.IsExecuted()));

        // 重新啟用並測試
        manager.SetEnabled(true);
        manager.Execute(TRADING_TICK);
        m_runner.RecordResult(Assert::AssertTrue("啟用後可執行", manager.IsExecuted()));

        delete manager;
    }
};
