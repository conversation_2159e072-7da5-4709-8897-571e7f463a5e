//+------------------------------------------------------------------+
//|                                      SimpleContainerTestRunner.mqh |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipelineContainer.mqh"
#include "../../TradingPipelineContainerManager.mqh"
#include "../../TradingPipeline.mqh"
#include "MockTradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 簡化版容器整合測試運行器                                           |
//+------------------------------------------------------------------+
class SimpleContainerIntegrationTestRunner : public TestRunner
{
public:
    // 構造函數
    SimpleContainerIntegrationTestRunner() : TestRunner() {}

    // 運行整合測試
    void RunIntegrationTests()
    {
        Print("🚀 開始執行 TradingPipelineContainer 整合測試...");

        // 運行基本工作流程測試
        TestBasicContainerWorkflow();

        // 運行容器管理器測試
        TestContainerManager();

        // 運行事件驅動測試
        TestEventDrivenExecution();

        // 運行錯誤處理測試
        TestErrorHandling();

        // 顯示摘要
        ShowSummary();

        Print("✅ TradingPipelineContainer 整合測試執行完成");
    }

private:
    // 測試基本容器工作流程
    void TestBasicContainerWorkflow()
    {
        Print("\n--- 測試基本容器工作流程 ---");

        // 創建容器
        TradingPipelineContainer* container = new TradingPipelineContainer(
            "基本測試容器", "用於基本工作流程測試", "BasicTestContainer", TRADING_TICK);

        // 創建模擬流水線
        MockTradingPipeline* pipeline1 = MockTradingPipelineFactory::CreateDataFeedPipeline("數據饋送");
        MockTradingPipeline* pipeline2 = MockTradingPipelineFactory::CreateSignalPipeline("信號分析");
        MockTradingPipeline* pipeline3 = MockTradingPipelineFactory::CreateOrderPipeline("訂單處理");

        // 測試容器基本屬性
        RecordResult(Assert::AssertEquals("基本工作流程_容器名稱", "基本測試容器", container.GetName()));
        RecordResult(Assert::AssertEquals("基本工作流程_容器類型", "BasicTestContainer", container.GetType()));
        RecordResult(Assert::AssertTrue("基本工作流程_初始為空", container.IsEmpty()));
        RecordResult(Assert::AssertFalse("基本工作流程_初始未滿", container.IsFull()));

        // 測試添加流水線
        bool added1 = container.AddPipeline(pipeline1);
        bool added2 = container.AddPipeline(pipeline2);
        bool added3 = container.AddPipeline(pipeline3);

        RecordResult(Assert::AssertTrue("基本工作流程_添加流水線1", added1));
        RecordResult(Assert::AssertTrue("基本工作流程_添加流水線2", added2));
        RecordResult(Assert::AssertTrue("基本工作流程_添加流水線3", added3));
        RecordResult(Assert::AssertEquals("基本工作流程_流水線數量", 3, container.GetPipelineCount()));
        RecordResult(Assert::AssertFalse("基本工作流程_不再為空", container.IsEmpty()));

        // 測試查找流水線
        ITradingPipeline* found = container.FindByName("數據饋送");
        RecordResult(Assert::AssertNotNull("基本工作流程_查找流水線", found));
        RecordResult(Assert::AssertTrue("基本工作流程_包含檢查", container.HasPipelineByName("信號分析")));

        // 測試執行容器
        RecordResult(Assert::AssertFalse("基本工作流程_執行前狀態", container.IsExecuted()));
        container.Execute();
        RecordResult(Assert::AssertTrue("基本工作流程_執行後狀態", container.IsExecuted()));

        // 驗證流水線執行
        RecordResult(Assert::AssertEquals("基本工作流程_流水線1執行次數", 1, pipeline1.GetExecutionCount()));
        RecordResult(Assert::AssertEquals("基本工作流程_流水線2執行次數", 1, pipeline2.GetExecutionCount()));
        RecordResult(Assert::AssertEquals("基本工作流程_流水線3執行次數", 1, pipeline3.GetExecutionCount()));

        // 測試重置
        container.Restore();
        RecordResult(Assert::AssertFalse("基本工作流程_重置後狀態", container.IsExecuted()));

        // 測試移除流水線
        bool removed = container.RemovePipelineByName("信號分析");
        RecordResult(Assert::AssertTrue("基本工作流程_移除流水線", removed));
        RecordResult(Assert::AssertEquals("基本工作流程_移除後數量", 2, container.GetPipelineCount()));

        delete container;
        Print("✓ 基本容器工作流程測試完成");
    }

    // 測試容器管理器
    void TestContainerManager()
    {
        Print("\n--- 測試容器管理器 ---");

        // 創建管理器
        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager(
            "測試管理器", "TestManager", false, 5);

        // 測試管理器基本屬性
        RecordResult(Assert::AssertEquals("容器管理器_名稱", "測試管理器", manager.GetName()));
        RecordResult(Assert::AssertEquals("容器管理器_類型", "TestManager", manager.GetType()));
        RecordResult(Assert::AssertEquals("容器管理器_最大容量", 5, manager.GetMaxContainers()));
        RecordResult(Assert::AssertTrue("容器管理器_初始為空", manager.IsEmpty()));

        // 創建不同事件類型的容器
        TradingPipelineContainer* initContainer = new TradingPipelineContainer(
            "初始化容器", "處理初始化", "InitContainer", TRADING_INIT);
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer(
            "Tick容器", "處理Tick", "TickContainer", TRADING_TICK);
        TradingPipelineContainer* deinitContainer = new TradingPipelineContainer(
            "清理容器", "處理清理", "DeinitContainer", TRADING_DEINIT);

        // 向容器添加流水線
        MockTradingPipeline* initPipeline = new MockTradingPipeline("初始化流水線", true, 10);
        MockTradingPipeline* tickPipeline = new MockTradingPipeline("Tick流水線", true, 5);
        MockTradingPipeline* deinitPipeline = new MockTradingPipeline("清理流水線", true, 15);

        initContainer.AddPipeline(initPipeline);
        tickContainer.AddPipeline(tickPipeline);
        deinitContainer.AddPipeline(deinitPipeline);

        // 測試添加容器
        bool addedInit = manager.AddContainer(initContainer);
        bool addedTick = manager.AddContainer(tickContainer);
        bool addedDeinit = manager.AddContainer(deinitContainer);

        RecordResult(Assert::AssertTrue("容器管理器_添加初始化容器", addedInit));
        RecordResult(Assert::AssertTrue("容器管理器_添加Tick容器", addedTick));
        RecordResult(Assert::AssertTrue("容器管理器_添加清理容器", addedDeinit));
        RecordResult(Assert::AssertEquals("容器管理器_容器數量", 3, manager.GetContainerCount()));

        // 測試按事件類型查找
        TradingPipelineContainer* foundInit = manager.FindContainerByEventType(TRADING_INIT);
        TradingPipelineContainer* foundTick = manager.FindContainerByEventType(TRADING_TICK);
        TradingPipelineContainer* foundDeinit = manager.FindContainerByEventType(TRADING_DEINIT);

        RecordResult(Assert::AssertNotNull("容器管理器_查找INIT容器", foundInit));
        RecordResult(Assert::AssertNotNull("容器管理器_查找TICK容器", foundTick));
        RecordResult(Assert::AssertNotNull("容器管理器_查找DEINIT容器", foundDeinit));

        // 測試按名稱查找
        TradingPipelineContainer* foundByName = manager.FindContainerByName("Tick容器");
        RecordResult(Assert::AssertNotNull("容器管理器_按名稱查找", foundByName));

        // 測試執行所有容器
        manager.ExecuteAll();
        RecordResult(Assert::AssertTrue("容器管理器_執行所有", manager.IsExecuted()));
        RecordResult(Assert::AssertTrue("容器管理器_初始化容器執行", initContainer.IsExecuted()));
        RecordResult(Assert::AssertTrue("容器管理器_Tick容器執行", tickContainer.IsExecuted()));
        RecordResult(Assert::AssertTrue("容器管理器_清理容器執行", deinitContainer.IsExecuted()));

        delete manager;
        Print("✓ 容器管理器測試完成");
    }

    // 測試事件驅動執行
    void TestEventDrivenExecution()
    {
        Print("\n--- 測試事件驅動執行 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("事件驅動管理器");

        // 創建不同事件類型的容器
        TradingPipelineContainer* initContainer = new TradingPipelineContainer("事件初始化", "", "EventInit", TRADING_INIT);
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer("事件Tick", "", "EventTick", TRADING_TICK);
        TradingPipelineContainer* deinitContainer = new TradingPipelineContainer("事件清理", "", "EventDeinit", TRADING_DEINIT);

        // 添加流水線
        MockTradingPipeline* initPipeline = new MockTradingPipeline("事件初始化流水線", true, 20);
        MockTradingPipeline* tickPipeline = new MockTradingPipeline("事件Tick流水線", true, 10);
        MockTradingPipeline* deinitPipeline = new MockTradingPipeline("事件清理流水線", true, 30);

        initContainer.AddPipeline(initPipeline);
        tickContainer.AddPipeline(tickPipeline);
        deinitContainer.AddPipeline(deinitPipeline);

        manager.AddContainer(initContainer);
        manager.AddContainer(tickContainer);
        manager.AddContainer(deinitContainer);

        // 測試按事件類型執行
        Print("  測試TRADING_INIT事件執行...");
        manager.Execute(TRADING_INIT);
        RecordResult(Assert::AssertTrue("事件驅動_INIT容器執行", initContainer.IsExecuted()));
        RecordResult(Assert::AssertFalse("事件驅動_TICK容器未執行", tickContainer.IsExecuted()));
        RecordResult(Assert::AssertFalse("事件驅動_DEINIT容器未執行", deinitContainer.IsExecuted()));

        Print("  測試TRADING_TICK事件執行...");
        manager.Execute(TRADING_TICK);
        RecordResult(Assert::AssertTrue("事件驅動_TICK容器執行", tickContainer.IsExecuted()));
        RecordResult(Assert::AssertFalse("事件驅動_DEINIT容器仍未執行", deinitContainer.IsExecuted()));

        Print("  測試TRADING_DEINIT事件執行...");
        manager.Execute(TRADING_DEINIT);
        RecordResult(Assert::AssertTrue("事件驅動_DEINIT容器執行", deinitContainer.IsExecuted()));

        // 驗證執行次數
        RecordResult(Assert::AssertEquals("事件驅動_INIT執行次數", 1, initPipeline.GetExecutionCount()));
        RecordResult(Assert::AssertEquals("事件驅動_TICK執行次數", 1, tickPipeline.GetExecutionCount()));
        RecordResult(Assert::AssertEquals("事件驅動_DEINIT執行次數", 1, deinitPipeline.GetExecutionCount()));

        // 測試重複Tick執行
        Print("  測試重複Tick執行...");
        for(int i = 0; i < 3; i++)
        {
            tickContainer.Restore();
            manager.Execute(TRADING_TICK);
        }
        RecordResult(Assert::AssertEquals("事件驅動_重複TICK執行次數", 4, tickPipeline.GetExecutionCount()));

        delete manager;
        Print("✓ 事件驅動執行測試完成");
    }

    // 測試錯誤處理
    void TestErrorHandling()
    {
        Print("\n--- 測試錯誤處理 ---");

        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("錯誤處理管理器");

        // 創建包含成功和失敗流水線的容器
        TradingPipelineContainer* errorContainer = new TradingPipelineContainer(
            "錯誤測試容器", "包含錯誤的容器", "ErrorContainer", TRADING_TICK);

        // 添加成功和失敗的流水線
        MockTradingPipeline* successPipeline = new MockTradingPipeline("成功流水線", true, 10);
        MockTradingPipeline* failPipeline = new MockTradingPipeline("失敗流水線", false, 15);
        MockTradingPipeline* exceptionPipeline = MockTradingPipelineFactory::CreateExceptionPipeline("異常流水線");

        errorContainer.AddPipeline(successPipeline);
        errorContainer.AddPipeline(failPipeline);
        errorContainer.AddPipeline(exceptionPipeline);

        manager.AddContainer(errorContainer);

        // 執行包含錯誤的容器
        Print("  執行包含錯誤的容器...");
        manager.Execute(TRADING_TICK);

        // 驗證容器仍然執行完成
        RecordResult(Assert::AssertTrue("錯誤處理_容器執行完成", errorContainer.IsExecuted()));
        RecordResult(Assert::AssertEquals("錯誤處理_成功流水線執行", 1, successPipeline.GetExecutionCount()));
        RecordResult(Assert::AssertEquals("錯誤處理_失敗流水線執行", 1, failPipeline.GetExecutionCount()));
        RecordResult(Assert::AssertEquals("錯誤處理_異常流水線執行", 1, exceptionPipeline.GetExecutionCount()));

        // 測試容器恢復
        errorContainer.Restore();
        RecordResult(Assert::AssertFalse("錯誤處理_恢復後未執行", errorContainer.IsExecuted()));

        // 測試空容器處理
        TradingPipelineContainer* emptyContainer = new TradingPipelineContainer("空容器");
        manager.AddContainer(emptyContainer);
        
        emptyContainer.Execute();
        RecordResult(Assert::AssertTrue("錯誤處理_空容器執行", emptyContainer.IsExecuted()));

        delete manager;
        Print("✓ 錯誤處理測試完成");
    }
};
