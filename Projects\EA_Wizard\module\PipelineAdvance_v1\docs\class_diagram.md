# PipelineAdvance_v1 模組類別圖

PipelineAdvance_v1 模組是一個簡化的流水線處理架構，實現了組合模式和模板方法模式，專注於交易流水線的管理和執行。

## 核心概念

- **ITradingPipeline**: 交易流水線介面，定義了基本的流水線操作
- **TradingPipeline**: 抽象基類，實現了 ITradingPipeline 介面的基本功能，包含 Manager 和 Stage
- **TradingPipelineContainer**: 統一的容器類，合併了原有 CompositePipeline 和 PipelineGroup 的功能
- **TradingPipelineContainerManager**: 簡化的管理器類別，使用動態容器管理多個 TradingPipelineContainer
- **PipelineResult**: 結果類，用於存儲流水線執行結果

## 類別圖

```mermaid
classDiagram
    %% 核心介面
    class ITradingPipeline {
        <<interface>>
        +Execute() void
        +GetName() string
        +GetType() string
        +IsExecuted() bool
        +Restore() void
    }

    %% 結果類
    class PipelineResult {
        -bool m_success
        -string m_message
        -string m_source
        -datetime m_timestamp
        -ENUM_ERROR_LEVEL m_errorLevel
        +PipelineResult(bool success, string message, string source, ENUM_ERROR_LEVEL errorLevel)
        +~PipelineResult()
        +IsSuccess() bool
        +GetMessage() string
        +GetSource() string
        +GetTimestamp() datetime
        +GetErrorLevel() ENUM_ERROR_LEVEL
        +ToString() string
    }

    %% 統一容器管理器
    class TradingPipelineContainerManager {
        -string m_name
        -string m_type
        -HashMap~string,TradingPipelineContainer*~ m_containers
        -bool m_owned
        -bool m_isEnabled
        -bool m_executed
        -int m_maxContainers
        +TradingPipelineContainerManager(string name, string type, bool owned, int maxContainers)
        +~TradingPipelineContainerManager()
        +AddContainer(TradingPipelineContainer* container) bool
        +RemoveContainer(TradingPipelineContainer* container) bool
        +RemoveContainerByName(string name) bool
        +FindContainerByName(string name) TradingPipelineContainer*
        +ExecuteAll() void
        +RestoreAll() void
        +Clear() void
        +GetContainerCount() int
        +GetMaxContainers() int
        +GetContainer(string name) TradingPipelineContainer*
        +GetAllContainers(TradingPipelineContainer* &containers[]) int
        +GetName() string
        +GetType() string
        +IsExecuted() bool
        +SetEnabled(bool enabled) void
        +IsEnabled() bool
        +HasEmptySlot() bool
        +IsEmpty() bool
        +IsFull() bool
        +GetStatusInfo() string
    }

    %% 抽象基類
    class TradingPipeline {
        <<abstract>>
        #string m_name
        #string m_type
        #bool m_executed
        #ENUM_TRADING_STAGE m_stage
        #TradingPipelineContainerManager* m_manager
        +TradingPipeline(string name, string type, ENUM_TRADING_STAGE stage, TradingPipelineContainerManager* manager)
        +~TradingPipeline()
        +Execute() void
        +GetName() string
        +GetType() string
        +IsExecuted() bool
        +Restore() void
        +GetStage() ENUM_TRADING_STAGE
        +GetManager() TradingPipelineContainerManager*
        #Main() void*
    }

    %% 統一的交易流水線容器
    class TradingPipelineContainer {
        -string m_name
        -string m_type
        -string m_description
        -bool m_executed
        -bool m_isEnabled
        -Vector~ITradingPipeline*~ m_pipelines
        -bool m_owned
        -int m_maxPipelines
        -PipelineResult* m_last_result
        +TradingPipelineContainer(string name, string description, string type, bool owned, int maxPipelines)
        +~TradingPipelineContainer()
        +Execute() void
        +GetName() string
        +GetType() string
        +IsExecuted() bool
        +Restore() void
        +AddPipeline(ITradingPipeline* pipeline) bool
        +RemovePipeline(ITradingPipeline* pipeline) bool
        +RemovePipelineByName(string name) bool
        +FindByName(string name, ITradingPipeline* defaultValue) ITradingPipeline*
        +GetPipeline(int index, ITradingPipeline* defaultValue) ITradingPipeline*
        +Clear() void
        +GetPipelineCount() int
        +GetMaxPipelines() int
        +HasPipeline(ITradingPipeline* pipeline) bool
        +HasPipelineByName(string name) bool
        +GetDescription() string
        +SetDescription(string description) void
        +SetEnabled(bool enabled) void
        +IsEnabled() bool
        +GetResult() PipelineResult*
        +GetStatusInfo() string
        +IsEmpty() bool
        +IsFull() bool
        +GetAllPipelines(ITradingPipeline* &pipelines[]) int
        +AddPipelines(ITradingPipeline* &pipelines[]) int
        +AreAllPipelinesExecuted() bool
    }

    %% 簡單實現示例
    class SimpleTradingPipeline {
        +SimpleTradingPipeline()
    }

    %% 枚舉類型
    class ENUM_TRADING_EVENT {
        <<enumeration>>
        TRADING_INIT
        TRADING_TICK
        TRADING_DEINIT
    }

    class ENUM_TRADING_STAGE {
        <<enumeration>>
        INIT_START
        INIT_PARAMETERS
        INIT_VARIABLES
        INIT_ENVIRONMENT
        INIT_INDICATORS
        INIT_COMPLETE
        TICK_DATA_FEED
        TICK_SIGNAL_ANALYSIS
        TICK_ORDER_MANAGEMENT
        TICK_RISK_CONTROL
        TICK_LOGGING
        DEINIT_CLEANUP
        DEINIT_SAVE_STATE
        DEINIT_COMPLETE
    }

    class ENUM_PIPELINE_STATUS {
        <<enumeration>>
        STATUS_PENDING
        STATUS_RUNNING
        STATUS_COMPLETED
        STATUS_FAILED
        STATUS_CANCELLED
    }

    class ENUM_ERROR_LEVEL {
        <<enumeration>>
        ERROR_LEVEL_INFO
        ERROR_LEVEL_WARNING
        ERROR_LEVEL_ERROR
        ERROR_LEVEL_CRITICAL
    }

    class ENUM_EXECUTION_MODE {
        <<enumeration>>
        EXECUTION_SEQUENTIAL
        EXECUTION_PARALLEL
        EXECUTION_CONDITIONAL
    }

    %% 工具類
    class TradingEventUtils {
        <<utility>>
        +EventToString(ENUM_TRADING_EVENT event) string$
        +StageToString(ENUM_TRADING_STAGE stage) string$
        +StatusToString(ENUM_PIPELINE_STATUS status) string$
        +IsValidEvent(ENUM_TRADING_EVENT event) bool$
        +IsStageOfEvent(ENUM_TRADING_STAGE stage, ENUM_TRADING_EVENT event) bool$
        +GetEventStages(ENUM_TRADING_EVENT event, ENUM_TRADING_STAGE &stages[]) int$
    }

    %% 關係
    ITradingPipeline <|.. TradingPipeline : implements
    ITradingPipeline <|.. TradingPipelineContainer : implements
    TradingPipeline <|-- SimpleTradingPipeline : extends
    TradingPipeline --> TradingPipelineContainerManager : uses
    TradingPipeline --> ENUM_TRADING_STAGE : uses
    TradingPipelineContainer o-- ITradingPipeline : contains
    TradingPipelineContainerManager o-- TradingPipelineContainer : manages (HashMap)
    TradingPipelineContainer --> PipelineResult : uses
    TradingEventUtils --> ENUM_TRADING_EVENT : uses
    TradingEventUtils --> ENUM_TRADING_STAGE : uses
    TradingEventUtils --> ENUM_PIPELINE_STATUS : uses
```

## 設計模式

### 1. 組合模式 (Composite Pattern)

- **TradingPipelineContainer** 實現了組合模式，可以包含多個子流水線
- 客戶端可以統一處理單個流水線和容器流水線
- **TradingPipelineContainer** 直接實現 **ITradingPipeline** 介面，支持嵌套

### 2. 模板方法模式 (Template Method Pattern)

- **TradingPipeline** 定義了執行流程的骨架
- 子類實現具體的 `Main()` 方法

### 3. 統一容器管理模式 (Unified Container Management Pattern)

- **TradingPipelineContainerManager** 使用 HashMap 進行高效容器管理
- 支持無限制數量的 **TradingPipelineContainer**（受 maxContainers 限制）
- 使用容器名稱作為 key，提供 O(1) 查找性能和更直觀的 API

### 4. 策略模式 (Strategy Pattern)

- 通過不同的 **ENUM_EXECUTION_MODE** 支持不同的執行策略
- 通過 **ENUM_TRADING_STAGE** 支持不同的交易階段
- 通過 **ENUM_TRADING_EVENT** 支持事件驅動的執行

## 核心特性

### 1. 統一的架構

- 合併了 CompositePipeline 和 PipelineGroup 的功能到 **TradingPipelineContainer**
- **TradingPipelineContainer** 直接實現 **ITradingPipeline** 介面
- 減少了約 70%的重複代碼，簡化了架構層次

### 2. 高效容器管理

- **TradingPipelineContainerManager** 使用 HashMap 進行容器管理
- 支持靈活的容器數量管理（不再限制為 3 個）
- 提供 O(1) 查找性能和基於名稱的直觀 API

### 3. 階段化處理

- **ENUM_TRADING_STAGE** 定義明確的交易階段
- **TradingPipeline** 包含階段信息和管理器引用
- 支持階段化的流水線執行

### 4. 簡化的執行模式

- 移除了容器級別的事件類型綁定，提高靈活性
- 支持統一的容器執行和重置
- 簡化了執行邏輯，減少複雜性

### 5. 類型安全

- 使用強類型的枚舉定義
- 明確的介面契約

### 6. 錯誤處理

- 統一的錯誤級別定義
- 詳細的執行結果記錄

### 7. 靈活的組織

- 統一的容器管理
- 支持動態添加和移除
- 支持嵌套容器結構

## 使用流程

1. **創建管理器**：創建 `TradingPipelineContainerManager` 實例
2. **創建流水線**：實現 `ITradingPipeline` 介面或繼承 `TradingPipeline`（包含階段和管理器）
3. **創建容器**：使用 `TradingPipelineContainer` 組合多個子流水線並設置業務屬性
4. **註冊到管理器**：將 `TradingPipelineContainer` 添加到 `TradingPipelineContainerManager`
5. **執行處理**：通過管理器統一執行所有容器或按名稱執行特定容器
6. **結果檢查**：通過 `PipelineResult` 檢查執行結果

## 常量定義

```mql4
const string TRADING_PIPELINE_TYPE = "TradingPipeline";
const string TRADING_PIPELINE_CONTAINER_TYPE = "TradingPipelineContainer";
const string LOGGING_PIPELINE_TYPE = "LoggingPipeline";
const string ERROR_HANDLING_PIPELINE_TYPE = "ErrorHandlingPipeline";

const int DEFAULT_MAX_PIPELINES = 50;
const int DEFAULT_MAX_CONTAINERS = 10;
const int DEFAULT_MAX_RETRIES = 3;
const int DEFAULT_TIMEOUT_MS = 5000;
```

## 使用示例

```mql4
// 創建具體的交易流水線
class DataFeedPipeline : public TradingPipeline
{
public:
    DataFeedPipeline(string name, TradingPipelineContainerManager* manager)
        : TradingPipeline(name, "DataFeed", TICK_DATA_FEED, manager) {}

protected:
    void Main() override
    {
        // 實現數據饋送邏輯
        Print("執行數據饋送: ", GetName(), ", 階段: ", EnumToString(GetStage()));
    }
};

class SignalPipeline : public TradingPipeline
{
public:
    SignalPipeline(string name, TradingPipelineContainerManager* manager)
        : TradingPipeline(name, "Signal", TICK_SIGNAL_ANALYSIS, manager) {}

protected:
    void Main() override
    {
        // 實現信號分析邏輯
        Print("執行信號分析: ", GetName(), ", 階段: ", EnumToString(GetStage()));
    }
};

// 使用示例
void OnStart()
{
    // 1. 創建管理器
    TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("主管理器");

    // 2. 創建基本流水線（包含階段和管理器）
    DataFeedPipeline* dataFeed = new DataFeedPipeline("市場數據", manager);
    SignalPipeline* signal = new SignalPipeline("交易信號", manager);

    // 3. 創建容器（統一了原有的 CompositePipeline 和 PipelineGroup 功能）
    TradingPipelineContainer* tickContainer = new TradingPipelineContainer(
        "Tick處理容器",
        "處理每個Tick的流水線容器",
        "TradingPipelineContainer"
    );

    // 4. 添加流水線到容器
    tickContainer.AddPipeline(dataFeed);
    tickContainer.AddPipeline(signal);

    // 5. 添加容器到管理器
    manager.AddContainer(tickContainer);

    // 6. 通過管理器執行所有容器
    manager.ExecuteAll();

    // 7. 檢查執行結果
    Print("管理器執行狀態: ", manager.IsExecuted());
    Print("管理器中容器數量: ", manager.GetContainerCount());
    Print("容器執行狀態: ", tickContainer.IsExecuted());
    Print("容器中流水線數量: ", tickContainer.GetPipelineCount());

    // 8. 清理資源
    delete manager;  // 會自動清理所有相關資源
}
```

## 優勢

1. **統一性**：合併了 CompositePipeline 和 PipelineGroup 功能，減少約 70%重複代碼
2. **簡潔性**：從 4 層架構簡化為 2 層架構，更易理解和維護
3. **高效性**：使用 HashMap 提供 O(1) 查找性能，比原有的 Vector 更高效
4. **靈活性**：移除容器級別的事件類型綁定，提高使用靈活性
5. **直觀性**：基於名稱的容器管理，API 更加直觀易用
6. **階段化**：明確的交易階段定義，支持階段化處理
7. **可維護性**：清晰的類別關係和職責分離
8. **可擴展性**：支持新的流水線類型和執行模式
9. **可測試性**：每個組件都可以獨立測試
10. **性能**：減少了不必要的抽象層和複雜邏輯，提高執行效率
11. **向後兼容**：提供完整的遷移指南和 API 對應關係

## 🔄 架構變化總結

### 舊架構 (複雜)

```
PipelineGroupManager
    └── PipelineGroup (事件類型、啟用狀態)
        └── CompositePipeline (實現ITradingPipeline)
            └── ITradingPipeline (具體流水線)
```

### 新架構 (簡化)

```
TradingPipelineContainerManager
    └── TradingPipelineContainer (統一容器，實現ITradingPipeline)
        └── ITradingPipeline (具體流水線)
```

### 主要改進

1. **✅ 架構統一**：TradingPipelineContainer 合併了 CompositePipeline 和 PipelineGroup 功能
2. **✅ 高效管理**：TradingPipelineContainerManager 使用 HashMap 提供 O(1) 查找性能
3. **✅ 簡化設計**：移除容器級別的事件類型綁定，提高靈活性
4. **✅ 減少重複**：消除約 70%的重複功能代碼
5. **✅ 簡化層次**：從 4 層減少到 2 層結構
6. **✅ 直觀 API**：基於名稱的容器管理，更符合實際使用場景
7. **✅ 增強功能**：保留所有核心功能並提升性能
