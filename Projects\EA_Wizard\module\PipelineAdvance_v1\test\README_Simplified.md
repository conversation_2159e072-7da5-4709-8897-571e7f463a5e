# PipelineAdvance_v1 簡化測試套件

## 📋 概述

本測試套件已經過簡化，移除了重複的.mq4 文件，提供統一的測試入口和清晰的結構。

## 🗂️ 簡化後的文件結構

```
test/
├── README_Simplified.md                    # 本文檔
├── RunTests.mq4                           # 🎯 統一測試入口（新增）
├── RunAllTests.mqh                        # 主要測試邏輯
├── TestFramework.mqh                      # 測試框架
├── unit/                                  # 單元測試
│   ├── TestCompositePipeline.mqh         # CompositePipeline 測試
│   ├── TestPipelineGroupManager.mqh      # PipelineGroupManager 測試
│   ├── TestTradingPipelineContainer.mqh  # TradingPipelineContainer 測試
│   └── TestTradingPipelineContainerManager.mqh # 容器管理器測試
└── integration/                           # 整合測試
    ├── README.md                          # 整合測試說明
    ├── MockTradingPipeline.mqh           # 模擬流水線
    ├── SimpleTestRunner.mqh              # 簡化整合測試運行器
    ├── SimpleTestRunner_v2.mqh           # 增強版整合測試運行器
    ├── SimpleContainerTestRunner.mqh     # 容器整合測試運行器
    └── RunSimpleContainerTests.mq4       # 容器專用測試入口
```

## 🚀 快速開始

### 運行完整測試套件

```mql4
// 使用統一入口（推薦）
#include "Projects/EA_Wizard/module/PipelineAdvance_v1/test/RunTests.mq4"

void OnStart()
{
    // 腳本會自動運行完整測試套件
}
```

### 選擇性測試執行

修改 `RunTests.mq4` 中的 `OnStart()` 函數來選擇不同的測試選項：

```mql4
void OnStart()
{
    // 選項 1: 完整測試套件（預設）
    RunAllPipelineAdvanceV1Tests();

    // 選項 2: 快速檢查
    // QuickPipelineAdvanceV1Check();

    // 選項 3: 僅單元測試
    // RunPipelineAdvanceV1UnitTests();

    // 選項 4: 僅整合測試
    // RunPipelineAdvanceV1IntegrationTests();

    // 選項 5: 比較測試版本
    // CompareSimpleTestRunners();
}
```

## 🧪 測試選項說明

### 1. 完整測試套件

- **函數**: `RunAllPipelineAdvanceV1Tests()`
- **包含**: 所有單元測試 + 所有整合測試
- **適用**: 完整驗證、發布前測試

### 2. 快速檢查

- **函數**: `QuickPipelineAdvanceV1Check()`
- **包含**: 核心功能快速驗證
- **適用**: 開發過程中的快速驗證

### 3. 單元測試

- **函數**: `RunPipelineAdvanceV1UnitTests()`
- **包含**: CompositePipeline, PipelineGroupManager, Container 測試
- **適用**: 單個組件驗證

### 4. 整合測試

- **函數**: `RunPipelineAdvanceV1IntegrationTests()`
- **包含**: 組件間協作測試
- **適用**: 系統整合驗證

### 5. 版本比較

- **函數**: `CompareSimpleTestRunners()`
- **包含**: v1 vs v2 測試運行器比較
- **適用**: 功能對比分析

## 🔧 已移除的文件

為了簡化測試套件，以下重複的.mq4 文件已被移除：

- ❌ `runalltests.mq4` - 過時，功能有限
- ❌ `TestSimpleRunnerV2.mq4` - 重複功能
- ❌ `unit/TestRunner.mq4` - 重複功能
- ❌ `unit/RunTestCompositePipeline.mq4` - 重複功能
- ❌ `unit/RunTestPipelineGroupManager.mq4` - 重複功能
- ❌ `integration/TestSimpleRunner.mq4` - 重複功能
- ❌ `integration/TestSimpleRunner_v2.mq4` - 重複功能
- ❌ `integration/RunIntegrationTests.mq4` - 重複功能
- ❌ `integration/RunTradingPipelineContainerIntegrationTests.mq4` - 重複功能

## ✅ 簡化的優勢

1. **統一入口**: 所有測試通過 `RunTests.mq4` 統一管理
2. **減少重複**: 移除了 9 個重複的.mq4 文件
3. **清晰結構**: 保留核心.mqh 文件，功能完整
4. **靈活選擇**: 提供多種測試執行選項
5. **易於維護**: 減少文件數量，降低維護複雜度

## 📚 進階使用

### 自定義測試組合

```mql4
void CustomTestSuite()
{
    Print("🎯 執行自定義測試組合...");

    // 只測試容器相關功能
    RunTestTradingPipelineContainer();
    RunTestTradingPipelineContainerManager();
    RunTradingPipelineContainerIntegrationTests();

    Print("✅ 自定義測試完成");
}
```

### 專項測試

```mql4
void FocusedTest()
{
    // PipelineGroupManager 專項測試
    RunPipelineGroupManagerFocusedTests();
}
```

## 🔍 故障排除

如果遇到問題，請檢查：

1. **文件路徑**: 確保所有 include 路徑正確
2. **依賴關係**: 確保相關模組文件存在
3. **編譯錯誤**: 檢查 MQL4 編譯器輸出
4. **測試結果**: 查看控制台輸出的詳細信息

## 📝 版本歷史

- **v2.0.0**: 簡化測試套件，移除重複文件，創建統一入口
- **v1.x.x**: 原始版本，包含多個重複的測試入口文件
