//+------------------------------------------------------------------+
//|                                                     RunTests.mq4 |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "RunAllTests.mqh"

//+------------------------------------------------------------------+
//| 腳本程序開始函數                                                 |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("\n" + StringRepeat("=", 80));
    Print("  PipelineAdvance_v1 測試套件");
    Print("  統一測試入口");
    Print(StringRepeat("=", 80));
    Print("開始時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 80));

    // 顯示可用的測試選項
    ShowTestOptions();
    
    // 運行完整測試套件（預設選項）
    RunAllPipelineAdvanceV1Tests();

    Print("\n" + StringRepeat("=", 80));
    Print("  PipelineAdvance_v1 測試套件完成");
    Print("結束時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 80));
}

//+------------------------------------------------------------------+
//| 顯示測試選項                                                     |
//+------------------------------------------------------------------+
void ShowTestOptions()
{
    Print("\n📋 可用的測試選項：");
    Print("   1. RunAllPipelineAdvanceV1Tests()           - 完整測試套件");
    Print("   2. RunAllPipelineAdvanceV1TestsSimple()     - 簡化版測試");
    Print("   3. QuickPipelineAdvanceV1Check()            - 快速檢查");
    Print("   4. RunPipelineAdvanceV1UnitTests()          - 僅單元測試");
    Print("   5. RunPipelineAdvanceV1IntegrationTests()   - 僅整合測試");
    Print("   6. RunSimpleTestRunnerV2Only()              - 僅 v2 整合測試");
    Print("   7. CompareSimpleTestRunners()               - 比較測試版本");
    Print("   8. RunPipelineGroupManagerFocusedTests()    - PipelineGroupManager 專項測試");
    Print("   9. RunTradingPipelineContainerIntegrationTests() - 容器整合測試");
    Print("\n💡 提示：修改 OnStart() 函數中的調用來選擇不同的測試選項");
    Print(StringRepeat("-", 80));
}

//+------------------------------------------------------------------+
//| 快速測試函數（供開發時使用）                                     |
//+------------------------------------------------------------------+
void QuickTest()
{
    Print("⚡ 執行快速測試...");
    bool result = QuickPipelineAdvanceV1Check();
    Print("快速測試結果: " + (result ? "✅ 通過" : "❌ 失敗"));
}

//+------------------------------------------------------------------+
//| 單元測試專用函數                                                 |
//+------------------------------------------------------------------+
void UnitTestsOnly()
{
    Print("🧪 執行單元測試...");
    RunPipelineAdvanceV1UnitTests();
}

//+------------------------------------------------------------------+
//| 整合測試專用函數                                                 |
//+------------------------------------------------------------------+
void IntegrationTestsOnly()
{
    Print("🔗 執行整合測試...");
    RunPipelineAdvanceV1IntegrationTests();
    RunPipelineAdvanceV1IntegrationTestsV2();
    RunTradingPipelineContainerIntegrationTests();
}

//+------------------------------------------------------------------+
//| 比較測試版本                                                     |
//+------------------------------------------------------------------+
void CompareVersions()
{
    Print("📊 比較測試版本...");
    CompareSimpleTestRunners();
}
