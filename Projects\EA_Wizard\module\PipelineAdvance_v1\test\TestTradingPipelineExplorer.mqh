//+------------------------------------------------------------------+
//|                                    TestTradingPipelineExplorer.mqh |
//|                                            EAPipelineAdvance_v1     |
//|                                                                      |
//+------------------------------------------------------------------+
#property strict

#include "../TradingPipelineExplorer.mqh"
#include "../TradingPipelineContainer.mqh"
#include "../TradingPipelineContainerManager.mqh"

//+------------------------------------------------------------------+
//| TradingPipelineExplorer 測試類                                   |
//+------------------------------------------------------------------+
class TestTradingPipelineExplorer
{
private:
    string m_testName;
    int m_totalTests;
    int m_passedTests;
    int m_failedTests;

public:
    // 構造函數
    TestTradingPipelineExplorer(string testName = "TradingPipelineExplorer測試")
        : m_testName(testName),
          m_totalTests(0),
          m_passedTests(0),
          m_failedTests(0)
    {
    }

    // 析構函數
    ~TestTradingPipelineExplorer() {}

    //+------------------------------------------------------------------+
    //| 運行所有測試                                                     |
    //+------------------------------------------------------------------+
    void RunAllTests()
    {
        Print("=== 開始 ", m_testName, " ===");
        
        TestConstructorAndBasicMethods();
        TestGetPipelineByStage();
        TestGetPipelineByEvent();
        TestStatisticsMethods();
        TestReportGeneration();
        TestUtilityMethods();
        
        PrintTestSummary();
    }

    //+------------------------------------------------------------------+
    //| 測試構造函數和基本方法                                           |
    //+------------------------------------------------------------------+
    void TestConstructorAndBasicMethods()
    {
        Print("--- 測試構造函數和基本方法 ---");
        
        // 創建管理器
        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("TestManager");
        
        // 測試構造函數
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(manager, "TestExplorer", "測試探索器", true);
        
        AssertTrue(explorer.IsValid(), "探索器應該有效");
        AssertEquals(explorer.GetName(), "TestExplorer", "探索器名稱應該正確");
        AssertEquals(explorer.GetDescription(), "測試探索器", "探索器描述應該正確");
        AssertNotNull(explorer.GetManager(), "管理器指針應該不為空");
        
        delete explorer; // 會自動刪除 manager 因為 owned=true
    }

    //+------------------------------------------------------------------+
    //| 測試根據階段獲取流水線                                           |
    //+------------------------------------------------------------------+
    void TestGetPipelineByStage()
    {
        Print("--- 測試根據階段獲取流水線 ---");
        
        // 創建測試環境
        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("TestManager");
        TradingPipelineContainer* initContainer = new TradingPipelineContainer("InitContainer", "初始化容器", INIT_START);
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer("TickContainer", "Tick容器", TICK_DATA_FEED);
        
        manager.SetContainer(TRADING_INIT, initContainer);
        manager.SetContainer(TRADING_TICK, tickContainer);
        
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(manager, "TestExplorer", "測試探索器", false);
        
        // 測試獲取流水線
        ITradingPipeline* initPipeline = explorer.GetPipeline(INIT_START);
        ITradingPipeline* tickPipeline = explorer.GetPipeline(TICK_DATA_FEED);
        ITradingPipeline* invalidPipeline = explorer.GetPipeline(DEINIT_CLEANUP);
        
        AssertNotNull(initPipeline, "應該能找到初始化階段的流水線");
        AssertNotNull(tickPipeline, "應該能找到Tick階段的流水線");
        AssertNull(invalidPipeline, "不存在的階段應該返回NULL");
        
        // 測試階段映射
        ENUM_TRADING_EVENT initEvent = explorer.GetEventFromStage(INIT_START);
        ENUM_TRADING_EVENT tickEvent = explorer.GetEventFromStage(TICK_DATA_FEED);
        
        AssertEquals((int)initEvent, (int)TRADING_INIT, "INIT_START應該映射到TRADING_INIT");
        AssertEquals((int)tickEvent, (int)TRADING_TICK, "TICK_DATA_FEED應該映射到TRADING_TICK");
        
        delete explorer;
        delete manager;
        delete initContainer;
        delete tickContainer;
    }

    //+------------------------------------------------------------------+
    //| 測試根據事件獲取流水線                                           |
    //+------------------------------------------------------------------+
    void TestGetPipelineByEvent()
    {
        Print("--- 測試根據事件獲取流水線 ---");
        
        // 創建測試環境
        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("TestManager");
        TradingPipelineContainer* initContainer = new TradingPipelineContainer("InitContainer", "初始化容器", INIT_START);
        
        manager.SetContainer(TRADING_INIT, initContainer);
        
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(manager, "TestExplorer", "測試探索器", false);
        
        // 測試獲取流水線
        ITradingPipeline* initPipeline = explorer.GetPipeline(TRADING_INIT);
        ITradingPipeline* tickPipeline = explorer.GetPipeline(TRADING_TICK);
        
        AssertNotNull(initPipeline, "應該能找到初始化事件的流水線");
        AssertNull(tickPipeline, "不存在的事件應該返回NULL");
        
        // 測試檢查方法
        AssertTrue(explorer.HasPipelineForEvent(TRADING_INIT), "應該存在初始化事件的流水線");
        AssertFalse(explorer.HasPipelineForEvent(TRADING_TICK), "不應該存在Tick事件的流水線");
        
        delete explorer;
        delete manager;
        delete initContainer;
    }

    //+------------------------------------------------------------------+
    //| 測試統計方法                                                     |
    //+------------------------------------------------------------------+
    void TestStatisticsMethods()
    {
        Print("--- 測試統計方法 ---");
        
        // 創建測試環境
        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("TestManager");
        TradingPipelineContainer* initContainer = new TradingPipelineContainer("InitContainer", "初始化容器", INIT_START);
        TradingPipelineContainer* tickContainer = new TradingPipelineContainer("TickContainer", "Tick容器", TICK_DATA_FEED);
        
        manager.SetContainer(TRADING_INIT, initContainer);
        manager.SetContainer(TRADING_TICK, tickContainer);
        
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(manager, "TestExplorer", "測試探索器", false);
        
        // 測試統計方法
        int initCount = explorer.GetPipelineCountByEvent(TRADING_INIT);
        int tickCount = explorer.GetPipelineCountByEvent(TRADING_TICK);
        int deinitCount = explorer.GetPipelineCountByEvent(TRADING_DEINIT);
        int totalCount = explorer.GetTotalPipelineCount();
        
        AssertEquals(initCount, 1, "初始化事件應該有1個流水線（容器本身）");
        AssertEquals(tickCount, 1, "Tick事件應該有1個流水線（容器本身）");
        AssertEquals(deinitCount, 0, "清理事件應該有0個流水線");
        AssertEquals(totalCount, 2, "總流水線數應該為2");
        
        delete explorer;
        delete manager;
        delete initContainer;
        delete tickContainer;
    }

    //+------------------------------------------------------------------+
    //| 測試報告生成                                                     |
    //+------------------------------------------------------------------+
    void TestReportGeneration()
    {
        Print("--- 測試報告生成 ---");
        
        // 創建測試環境
        TradingPipelineContainerManager* manager = new TradingPipelineContainerManager("TestManager");
        TradingPipelineContainer* initContainer = new TradingPipelineContainer("InitContainer", "初始化容器", INIT_START);
        
        manager.SetContainer(TRADING_INIT, initContainer);
        
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(manager, "TestExplorer", "測試探索器", false);
        
        // 測試報告生成
        string explorationReport = explorer.GenerateExplorationReport();
        string stageReport = explorer.GenerateStageReport();
        
        AssertTrue(StringLen(explorationReport) > 0, "探索報告應該不為空");
        AssertTrue(StringLen(stageReport) > 0, "階段報告應該不為空");
        AssertTrue(StringFind(explorationReport, "TestExplorer") >= 0, "探索報告應該包含探索器名稱");
        AssertTrue(StringFind(stageReport, "INIT_START") >= 0, "階段報告應該包含階段信息");
        
        Print("探索報告預覽：");
        Print(StringSubstr(explorationReport, 0, 200), "...");
        
        delete explorer;
        delete manager;
        delete initContainer;
    }

    //+------------------------------------------------------------------+
    //| 測試工具方法                                                     |
    //+------------------------------------------------------------------+
    void TestUtilityMethods()
    {
        Print("--- 測試工具方法 ---");
        
        TradingPipelineContainerManager* manager1 = new TradingPipelineContainerManager("Manager1");
        TradingPipelineContainerManager* manager2 = new TradingPipelineContainerManager("Manager2");
        
        TradingPipelineExplorer* explorer = new TradingPipelineExplorer(manager1, "TestExplorer", "測試探索器", false);
        
        // 測試設置方法
        explorer.SetName("NewName");
        explorer.SetDescription("新描述");
        
        AssertEquals(explorer.GetName(), "NewName", "名稱應該已更新");
        AssertEquals(explorer.GetDescription(), "新描述", "描述應該已更新");
        
        // 測試設置新管理器
        bool result = explorer.SetManager(manager2, true);
        AssertTrue(result, "設置新管理器應該成功");
        AssertEquals(explorer.GetManager(), manager2, "管理器應該已更新");
        
        delete explorer; // 會自動刪除 manager2 因為 owned=true
        delete manager1;
    }

    //+------------------------------------------------------------------+
    //| 斷言方法                                                         |
    //+------------------------------------------------------------------+
    void AssertTrue(bool condition, string message)
    {
        m_totalTests++;
        if(condition)
        {
            m_passedTests++;
            Print("✓ PASS: ", message);
        }
        else
        {
            m_failedTests++;
            Print("✗ FAIL: ", message);
        }
    }

    void AssertFalse(bool condition, string message)
    {
        AssertTrue(!condition, message);
    }

    void AssertEquals(string actual, string expected, string message)
    {
        AssertTrue(actual == expected, message + StringFormat(" (期望: '%s', 實際: '%s')", expected, actual));
    }

    void AssertEquals(int actual, int expected, string message)
    {
        AssertTrue(actual == expected, message + StringFormat(" (期望: %d, 實際: %d)", expected, actual));
    }

    void AssertNotNull(void* pointer, string message)
    {
        AssertTrue(pointer != NULL, message);
    }

    void AssertNull(void* pointer, string message)
    {
        AssertTrue(pointer == NULL, message);
    }

    //+------------------------------------------------------------------+
    //| 打印測試摘要                                                     |
    //+------------------------------------------------------------------+
    void PrintTestSummary()
    {
        Print("=== ", m_testName, " 測試摘要 ===");
        Print("總測試數: ", m_totalTests);
        Print("通過: ", m_passedTests);
        Print("失敗: ", m_failedTests);
        Print("成功率: ", StringFormat("%.1f%%", (double)m_passedTests / m_totalTests * 100));
        
        if(m_failedTests == 0)
        {
            Print("🎉 所有測試通過！");
        }
        else
        {
            Print("⚠️ 有 ", m_failedTests, " 個測試失敗");
        }
    }
};
